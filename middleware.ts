import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createServerClient } from '@supabase/ssr'

export async function middleware(request: NextRequest) {
    const response = NextResponse.next()
    const { pathname } = request.nextUrl

    // 不需要驗證的路徑
    const publicPaths = [
        '/',
        '/auth/login',
        '/auth/register',
        '/auth/callback',
        '/api/auth/callback',
        '/explore',
        '/topic',
        '/thread',
        '/about',
        '/tag',
        '/test-auth',
        '/library',
        '/debug-auth',
    ]

    // 公開的 API 路由（不需要認證）
    const publicApiPaths = [
        '/api/reactions/count',
        '/api/reactions/check',
        '/api/auth/session',
        '/api/debug/cookies',
        '/api/topics',
        '/api/threads',
        '/api/cards',
        '/api/comments',
        '/api/explore',
        '/api/trending-topics',
        '/api/subtopics',
    ]

    // 檢查是否為公開路徑
    const isPublicPath = publicPaths.some(path =>
        pathname === path || pathname.startsWith(`${path}/`)
    )

    // 檢查是否為公開 API
    const isPublicApi = publicApiPaths.some(path =>
        pathname === path || pathname.startsWith(`${path}/`)
    )

    // 靜態資源不需要驗證
    const isStaticResource = pathname.startsWith('/_next') ||
        pathname.startsWith('/favicon') ||
        pathname.includes('.') && !pathname.startsWith('/api/')

    // 如果是公開路徑、公開 API 或靜態資源，直接返回
    if (isPublicPath || isPublicApi || isStaticResource) {
        return response
    }

    // 創建 Supabase 客戶端
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

    if (!supabaseUrl || !supabaseKey) {
        console.error('Missing Supabase environment variables in middleware')
        return response
    }

    const supabase = createServerClient(supabaseUrl, supabaseKey, {
        cookies: {
            getAll() {
                return request.cookies.getAll()
            },
            setAll(cookiesToSet) {
                cookiesToSet.forEach(({ name, value, options }) => {
                    request.cookies.set(name, value)
                    response.cookies.set(name, value, {
                        ...options,
                        secure: process.env.NODE_ENV === 'production',
                        sameSite: 'lax',
                        path: '/'
                    })
                })
            },
        },
    })

    try {
        // 檢查用戶認證狀態
        const { data: { user }, error } = await supabase.auth.getUser()

        if (error || !user) {
            // 如果是 API 路由，返回 401
            if (pathname.startsWith('/api/')) {
                return NextResponse.json(
                    { error: 'Unauthorized' },
                    { status: 401 }
                )
            }

            // 頁面路由重定向到登入頁
            const redirectUrl = new URL('/auth/login', request.url)
            redirectUrl.searchParams.set('redirectTo', pathname)
            return NextResponse.redirect(redirectUrl)
        }

    } catch (error) {
        console.error('Middleware error:', error)

        // API 路由發生錯誤時返回 500
        if (pathname.startsWith('/api/')) {
            return NextResponse.json(
                { error: 'Internal Server Error' },
                { status: 500 }
            )
        }

        // 頁面路由發生錯誤時重定向到登入頁
        const redirectUrl = new URL('/auth/login', request.url)
        return NextResponse.redirect(redirectUrl)
    }

    return response
}

// 匹配所有路徑，但在 middleware 內部進行過濾
export const config = {
    matcher: [
        /*
         * 匹配所有路徑除了：
         * - api (API routes)
         * - _next/static (static files)
         * - _next/image (image optimization files)
         * - favicon.ico (favicon file)
         */
        '/((?!_next/static|_next/image|favicon.ico).*)',
    ],
}