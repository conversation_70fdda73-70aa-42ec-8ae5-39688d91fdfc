"use client"

import React from "react"

import { useState, createContext, useContext } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import {
  BookmarkIcon,
  MessageSquare,
  Share2,
  ThumbsUp,
  ThumbsDown,
  ExternalLink,
  LightbulbIcon,
  AlertTriangle,
  Wrench,
  BookOpen,
  FlaskConical,
  HelpCircle,
  Zap,
  Eye,
} from "lucide-react"

// 首先，在文件顶部的 import 部分添加 NodeViewWrapper
import { NodeViewWrapper } from "@tiptap/react"

import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { cn } from "@/lib/utils"
import { RichTextContent } from "@/components/rich-text-content"
import { useAuth } from "@/contexts/auth-context"
import { useToast } from "@/components/ui/use-toast"
import { BookmarkButton } from "@/components/bookmark-button"

// 內容類型
export type ContentType = "viewpoint" | "thread"

// 變體類型
export type CardVariant = "card" | "compact" | "grid" | "quote"

// 觀點卡語義類型
export type ViewpointSemanticType = "insight" | "experience" | "guide" | "trap" | "debate" | "concept"

// 討論串語義類型
export type ThreadSemanticType = "discussion" | "question" | "brainstorm" | "chat"

// 統一語義類型
export type SemanticType = ViewpointSemanticType | ThreadSemanticType

// 卡片來源類型 - 更新為使用 ContributionType
export type ContributionType = "top_author" | "community" | "curated" | "original" | "others"

// 互動功能
export type Feature = "like" | "dislike" | "reply" | "bookmark" | "share"

// 作者接口
export interface Author {
  id: string
  name: string
  avatar?: string
}

// 統計數據接口
export interface ContentStats {
  likes?: number
  dislikes?: number
  comments?: number
  replies?: number
  views?: number
  bookmarks?: number
}

// 添加 CardQuoteProps 接口定义，在 ContentCardProps 之前
interface CardQuoteProps {
  node?: {
    attrs?: {
      cardId?: number | string
      cardTitle?: string
      cardContent?: string
      cardAuthor?: string
      cardType?: string
      cardTags?: string[]
      isLeader?: boolean
    }
  }
  card?: {
    id: number
    title: string
    content: string
    author: string
    type: string
    tags: string[]
    isLeader: boolean
  }
}

// 基礎內容卡片屬性
export interface ContentCardBaseProps {
  id: number
  title: string
  content: string
  author: Author
  timestamp?: string
  topics?: string[]
  subtopics?: string[]
  tags?: string[]
  stats?: ContentStats
  variant?: CardVariant
  className?: string
  isCompact?: boolean
  truncate?: boolean
  isPreview?: boolean
  isInteractive?: boolean
  features?: Feature[]
  onAction?: (action: string, id: number) => void
  forceBookmarked?: boolean // 強制顯示為已收藏狀態，用於 library 頁面
}

// 觀點卡特有屬性 - 更新為使用 contribution_type 和 card_type
export interface ViewpointCardProps extends ContentCardBaseProps {
  contentType: "viewpoint"
  semanticType: ViewpointSemanticType
  card_type?: "internal" | "external"
  contribution_type?: ContributionType
  originalSource?: string
  originalAuthor?: string // 添加原作者字段
}

// 討論串卡特有屬性
export interface ThreadCardProps extends ContentCardBaseProps {
  contentType: "thread"
  semanticType?: ThreadSemanticType
  status?: "draft" | "published" | "pending"
  isHot?: boolean
  isNew?: boolean
}

// 統一內容卡片屬性
export type ContentCardProps = ViewpointCardProps | ThreadCardProps

// 語義類型配置
const semanticTypeConfig: Record<
  SemanticType,
  {
    icon: React.ReactNode
    label: string
    description: string
    color: string
  }
> = {
  // 觀點卡語義類型
  insight: {
    icon: <LightbulbIcon className="h-3.5 w-3.5" />,
    label: "看法",
    description: "對該主題的觀點、價值判斷、立場",
    color: "bg-amber-50 text-amber-600 dark:bg-amber-950 dark:text-amber-300",
  },
  experience: {
    icon: <FlaskConical className="h-3.5 w-3.5" />,
    label: "實測經驗",
    description: "自己做過的實驗結果、性能比較、效果說明",
    color: "bg-purple-50 text-purple-600 dark:bg-purple-950 dark:text-purple-300",
  },
  guide: {
    icon: <Wrench className="h-3.5 w-3.5" />,
    label: "工具教學",
    description: "流程教學、步驟說明、指令解說",
    color: "bg-blue-50 text-blue-600 dark:bg-blue-950 dark:text-blue-300",
  },
  trap: {
    icon: <AlertTriangle className="h-3.5 w-3.5" />,
    label: "踩坑警示",
    description: "問題踩雷分享、Debug 解法、環境配置爛點",
    color: "bg-red-50 text-red-600 dark:bg-red-950 dark:text-red-300",
  },
  debate: {
    icon: <HelpCircle className="h-3.5 w-3.5" />,
    label: "爭議論點",
    description: "值不值得做？哪個方法比較爛？",
    color: "bg-orange-50 text-orange-600 dark:bg-orange-950 dark:text-orange-300",
  },
  concept: {
    icon: <BookOpen className="h-3.5 w-3.5" />,
    label: "概念整理",
    description: "原理、理論、詞彙解釋、系統性知識輸出",
    color: "bg-green-50 text-green-600 dark:bg-green-950 dark:text-green-300",
  },

  // 討論語義類型
  discussion: {
    icon: <MessageSquare className="h-3.5 w-3.5" />,
    label: "討論",
    description: "一般討論主題",
    color: "bg-blue-50 text-blue-600 dark:bg-blue-950 dark:text-blue-300",
  },
  question: {
    icon: <HelpCircle className="h-3.5 w-3.5" />,
    label: "問題",
    description: "尋求解答的問題",
    color: "bg-red-50 text-red-600 dark:bg-red-950 dark:text-red-300",
  },
  brainstorm: {
    icon: <Zap className="h-3.5 w-3.5" />,
    label: "集思廣益",
    description: "集體思考和創意討論",
    color: "bg-amber-50 text-amber-600 dark:bg-amber-950 dark:text-amber-300",
  },
  chat: {
    icon: <MessageSquare className="h-3.5 w-3.5" />,
    label: "閒聊",
    description: "輕鬆的交流討論",
    color: "bg-green-50 text-green-600 dark:bg-green-950 dark:text-green-300",
  },
}

// 卡片來源類型配置 - 更新為使用 ContributionType
const contributionTypeConfig: Record<
  ContributionType,
  {
    label: string
    badge: string
    color: string
    show: boolean
  }
> = {
  top_author: {
    label: "意見領袖",
    badge: "Leader",
    color: "bg-gradient-to-r from-amber-500 to-orange-500 text-white",
    show: true,
  },
  community: {
    label: "社群貢獻",
    badge: "Community",
    color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
    show: true,
  },
  curated: {
    label: "系統整理",
    badge: "Curated",
    color: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
    show: true,
  },
  original: {
    label: "原創內容",
    badge: "原創內容",
    color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
    show: true,
  },
  others: {
    label: "他人觀點",
    badge: "",
    color: "",
    show: false,
  },
}

// 上下文
interface ContentCardContextType {
  contentType: ContentType
  variant: CardVariant
  isInteractive: boolean
  features: Feature[]
  semanticType?: SemanticType
  status?: string
}

const ContentCardContext = createContext<ContentCardContextType>({
  contentType: "viewpoint",
  variant: "card",
  isInteractive: true,
  features: ["like", "dislike", "reply", "bookmark", "share"],
})

// 使用上下文的 Hook
const useContentCard = () => useContext(ContentCardContext)

// 標題組件
function ContentTitle({
  title,
  id,
  contentType,
  originalAuthor,
}: { title: string; id: number; contentType: ContentType; originalAuthor?: string }) {
  const { variant } = useContentCard()
  const path = contentType === "viewpoint" ? `/card/${id}` : `/thread/${id}`

  // 如果有原作者且是觀點卡，則顯示 "原作者：標題" 的格式
  const displayTitle = contentType === "viewpoint" && originalAuthor ? `${originalAuthor}：${title}` : title

  return (
    <Link href={path} className="hover:underline">
      <h3
        className={`${variant === "card" ? "text-lg font-bold" : variant === "grid" ? "text-sm" : "text-base"
          } leading-snug ${variant === "grid" ? "line-clamp-2" : "line-clamp-2"}`}
      >
        {displayTitle}
      </h3>
    </Link>
  )
}

// 作者和時間信息組件
function ContentMeta({ author, timestamp }: { author: Author; timestamp?: string }) {
  const { variant } = useContentCard()

  return (
    <div className={`flex items-center gap-2 ${variant === "grid" ? "text-xs" : "text-sm"} text-muted-foreground`}>
      {variant !== "grid" && (
        <Avatar className="h-6 w-6">
          <AvatarImage src={author.avatar || "/placeholder.svg"} alt={author.name} />
          <AvatarFallback>{author.name.charAt(0)}</AvatarFallback>
        </Avatar>
      )}
      <span>{author.name}</span>
      {timestamp && (
        <>
          <span>•</span>
          <span>{timestamp}</span>
        </>
      )}
    </div>
  )
}

// 內容組件
function ContentBody({ content }: { content: string }) {
  const { variant, contentType } = useContentCard()

  // 添加调试信息
  console.log("ContentBody received:", {
    contentType,
    contentLength: content ? content.length : 0,
    contentSample: content ? content.substring(0, 50) : null,
    isNull: content === null,
    isUndefined: content === undefined,
    isEmptyString: content === "",
  })

  // 确保内容是字符串
  const safeContent = typeof content === "string" ? content : ""

  // 如果内容为空，显示占位符
  if (!safeContent.trim()) {
    return (
      <div
        className={`text-muted-foreground italic ${variant === "card"
          ? "line-clamp-3"
          : variant === "compact"
            ? "line-clamp-1"
            : variant === "grid"
              ? "line-clamp-2 text-xs"
              : "line-clamp-4"
          }`}
      >
        {contentType === "viewpoint" ? "无观点内容" : "无讨论内容"}
      </div>
    )
  }

  return (
    <div
      className={`text-muted-foreground ${variant === "card"
        ? "line-clamp-3"
        : variant === "compact"
          ? "line-clamp-1"
          : variant === "grid"
            ? "line-clamp-2 text-xs"
            : "line-clamp-4"
        }`}
    >
      <RichTextContent content={safeContent} className="overflow-visible" />
    </div>
  )
}

// 標籤組件
function ContentTags({ tags, subtopics }: { tags?: string[]; subtopics?: string[] }) {
  const { variant, contentType } = useContentCard()

  // 使用 subtopics（觀點卡）或 tags（討論）
  const tagList = contentType === "viewpoint" ? subtopics : tags

  if (!tagList || tagList.length === 0) return null

  // Grid 變體只顯示最多2個標籤
  const maxTags = variant === "grid" ? 2 : 3
  const displayTags = tagList.slice(0, maxTags)
  const remainingTags = tagList.length > maxTags ? tagList.length - maxTags : 0

  return (
    <div className={`flex flex-wrap gap-${variant === "grid" ? "1" : "1.5"} mt-${variant === "grid" ? "1" : "3"}`}>
      {displayTags.map((tag) => (
        <Badge
          key={tag}
          variant="outline"
          className={`hover:bg-secondary ${variant === "grid" ? "text-[10px]" : "text-xs"} ${variant === "card" ? "font-medium" : "font-normal"}`}
        >
          #{tag}
        </Badge>
      ))}
      {remainingTags > 0 && (
        <Badge
          variant="outline"
          className={`hover:bg-secondary ${variant === "grid" ? "text-[10px]" : "text-xs"} ${variant === "card" ? "font-medium" : "font-normal"}`}
        >
          +{remainingTags}
        </Badge>
      )}
    </div>
  )
}

// 主題標籤組件
function TopicBadges({ topics }: { topics?: string[] }) {
  const { variant } = useContentCard()

  if (!topics || topics.length === 0) return null

  return (
    <div className="flex flex-wrap gap-2">
      {topics.slice(0, 1).map((topic) => (
        <Badge
          key={topic}
          variant="secondary"
          className={`flex items-center gap-1 ${variant === "grid" ? "text-xs py-0" : ""}`}
        >
          <span>{topic}</span>
        </Badge>
      ))}
    </div>
  )
}

// 檢查字符串是否為有效的 UUID
function isValidUUID(uuid: string) {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(uuid)
}

// 統計信息組件 - 修改為具有互動功能
function ContentStats({ stats, id, forceBookmarked }: { stats?: ContentStats; id: number; forceBookmarked?: boolean }) {
  const { variant, contentType, features, isInteractive } = useContentCard()
  const { isAuthenticated } = useAuth()
  const { toast } = useToast()
  const [hasLiked, setHasLiked] = useState(false)
  const [hasDisliked, setHasDisliked] = useState(false)
  const [likeCount, setLikeCount] = useState(stats?.likes || 0)
  const [dislikeCount, setDislikeCount] = useState(stats?.dislikes || 0)
  const [isProcessing, setIsProcessing] = useState(false)

  // 確保 features 是一個數組
  const safeFeatures = Array.isArray(features) ? features : ["like", "dislike", "reply", "bookmark", "share"]

  // 處理點讚/不認同
  const handleReaction = async (reactionType: "like" | "dislike", e: React.MouseEvent) => {
    e.stopPropagation()
    e.preventDefault()

    if (!isAuthenticated) {
      toast({
        title: "請先登入",
        description: "您需要登入才能進行此操作",
        variant: "destructive",
      })
      return
    }

    if (isProcessing) {
      return // 防止重複處理
    }

    setIsProcessing(true)

    // 確保 id 是字符串並檢查格式
    const itemId = typeof id === "number" ? id.toString() : id
    if (!isValidUUID(itemId)) {
      console.log("跳過反應操作：無效的 UUID 格式", itemId)
      setIsProcessing(false)
      return
    }

    // 先更新 UI，提供即時反饋
    if (reactionType === "like") {
      setHasLiked(!hasLiked)
      if (hasLiked) {
        setLikeCount((prev) => Math.max(0, prev - 1))
      } else {
        setLikeCount((prev) => prev + 1)
        // 如果用戶之前有 dislike，現在點 like，需要移除 dislike
        if (hasDisliked) {
          setHasDisliked(false)
          setDislikeCount((prev) => Math.max(0, prev - 1))
        }
      }
    } else {
      setHasDisliked(!hasDisliked)
      if (hasDisliked) {
        setDislikeCount((prev) => Math.max(0, prev - 1))
      } else {
        setDislikeCount((prev) => prev + 1)
        // 如果用戶之前有 like，現在點 dislike，需要移除 like
        if (hasLiked) {
          setHasLiked(false)
          setLikeCount((prev) => Math.max(0, prev - 1))
        }
      }
    }

    try {
      const response = await fetch("/api/reactions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          itemType: contentType === "viewpoint" ? "card" : "thread",
          itemId: itemId,
          reactionType,
        }),
      })

      const data = await response.json()

      if (!response.ok || !data.success) {
        // 如果請求失敗，恢復原來的狀態
        if (reactionType === "like") {
          setHasLiked(!hasLiked)
          setLikeCount((prev) => (hasLiked ? prev + 1 : Math.max(0, prev - 1)))
        } else {
          setHasDisliked(!hasDisliked)
          setDislikeCount((prev) => (hasDisliked ? prev + 1 : Math.max(0, prev - 1)))
        }

        toast({
          title: "操作失敗",
          description: data.error || "請稍後再試",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error toggling reaction:", error)

      // 如果請求失敗，恢復原來的狀態
      if (reactionType === "like") {
        setHasLiked(!hasLiked)
        setLikeCount((prev) => (hasLiked ? prev + 1 : Math.max(0, prev - 1)))
      } else {
        setHasDisliked(!hasDisliked)
        setDislikeCount((prev) => (hasDisliked ? prev + 1 : Math.max(0, prev - 1)))
      }

      toast({
        title: "操作失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      // 立即關閉處理中狀態
      setIsProcessing(false)
    }
  }

  // 處理分享功能
  const handleShare = (e: React.MouseEvent) => {
    if (e) {
      e.stopPropagation()
      e.preventDefault()
    }

    const url = window.location.origin + (contentType === "viewpoint" ? `/card/${id}` : `/thread/${id}`)

    if (navigator.share) {
      navigator.share({
        title: "分享內容",
        url: url,
      }).catch(console.error)
    } else {
      // 降級方案：複製到剪貼板
      navigator.clipboard.writeText(url).then(() => {
        toast({
          title: "已複製鏈接",
          description: "鏈接已複製到剪貼板",
        })
      }).catch(() => {
        toast({
          title: "複製失敗",
          description: "無法複製鏈接，請手動複製",
          variant: "destructive",
        })
      })
    }
  }

  // 獲取用戶反應狀態
  React.useEffect(() => {
    if (!isAuthenticated) return

    const fetchReactionStatus = async () => {
      try {
        // 確保 id 是字符串並檢查格式
        const itemId = typeof id === "number" ? id.toString() : id
        if (!isValidUUID(itemId)) {
          console.log("跳過反應狀態檢查：無效的 UUID 格式", itemId)
          return
        }

        // 檢查用戶是否已點讚
        const likeRes = await fetch(
          `/api/reactions/check?itemType=${contentType === "viewpoint" ? "card" : "thread"}&itemId=${itemId}&reactionType=like`,
        )

        // 檢查響應是否成功
        if (!likeRes.ok) {
          console.error("Error checking like status:", await likeRes.text())
          return
        }

        const likeData = await likeRes.json()
        if (likeData.success) {
          setHasLiked(!!likeData.data.hasReacted)
        }
      } catch (error) {
        console.error("Error checking reaction status:", error)
      }
    }

    fetchReactionStatus()
  }, [id, contentType, isAuthenticated])

  // 獲取最新的反應計數
  React.useEffect(() => {
    const fetchReactionCounts = async () => {
      try {
        // 確保 id 是字符串並檢查格式
        const itemId = typeof id === "number" ? id.toString() : id
        if (!isValidUUID(itemId)) {
          console.log("跳過反應計數獲取：無效的 UUID 格式", itemId)
          return
        }

        const countRes = await fetch(
          `/api/reactions/count?itemType=${contentType === "viewpoint" ? "card" : "thread"}&itemId=${itemId}`,
        )

        // 檢查響應是否成功
        if (!countRes.ok) {
          console.error("Error fetching reaction counts:", await countRes.text())
          return
        }

        const countData = await countRes.json()

        if (countData.success) {
          setLikeCount(countData.data.like || 0)
        }
      } catch (error) {
        console.error("Error fetching reaction counts:", error)
      }
    }

    fetchReactionCounts()
  }, [id, contentType])

  // Grid 變體只顯示評論/回覆數
  if (variant === "grid") {
    const commentCount = contentType === "viewpoint" ? stats?.comments : stats?.replies
    return commentCount !== undefined ? (
      <div className="flex items-center gap-1 text-xs text-muted-foreground">
        <MessageSquare className="h-3 w-3" />
        <span>{commentCount}</span>
      </div>
    ) : null
  }

  return (
    <div className="flex items-center gap-4 text-xs text-muted-foreground">
      {safeFeatures.includes("like") && (
        <button
          className={cn(
            "flex items-center gap-1 cursor-pointer transition-colors",
            hasLiked ? "text-primary" : "hover:text-primary/70",
          )}
          onClick={(e) => handleReaction("like", e)}
          disabled={isProcessing}
          aria-label={`${likeCount} 個`}
          title={hasLiked ? "取消讚" : "點讚"}
        >
          <ThumbsUp className={cn("h-3.5 w-3.5", hasLiked && "fill-current")} />
          <span>{likeCount}</span>
        </button>
      )}

      {safeFeatures.includes("dislike") && contentType === "viewpoint" && (
        <button
          className={cn(
            "flex items-center gap-1 cursor-pointer transition-colors",
            hasDisliked ? "text-primary" : "hover:text-primary/70",
          )}
          onClick={(e) => handleReaction("dislike", e)}
          disabled={isProcessing}
          aria-label={`${dislikeCount} 個不認同`}
          title={hasDisliked ? "取消不認同" : "不認同"}
        >
          <ThumbsDown className={cn("h-3.5 w-3.5", hasDisliked && "fill-current")} />
          <span>{dislikeCount}</span>
        </button>
      )}

      {(stats?.comments !== undefined || stats?.replies !== undefined) && (
        <div className="flex items-center gap-1" aria-label={`${stats.comments || stats.replies} 個評論`}>
          <MessageSquare className="h-3.5 w-3.5" />
          <span>{stats?.comments || stats?.replies}</span>
        </div>
      )}

      {stats?.views !== undefined && (
        <div className="flex items-center gap-1" aria-label={`${stats.views} 次瀏覽`}>
          <Eye className="h-3.5 w-3.5" />
          <span>{stats.views}</span>
        </div>
      )}

      {/* 收藏按鈕 */}
      {safeFeatures.includes("bookmark") && isInteractive && (
        <div onClick={(e) => e.stopPropagation()}>
          <BookmarkButton
            itemId={id.toString()}
            itemType={contentType === "viewpoint" ? "card" : "thread"}
            showCount={true}
            disabled={!isAuthenticated}
            className="text-xs text-muted-foreground hover:text-primary/70"
            forceBookmarked={forceBookmarked}
          />
        </div>
      )}

      {/* 分享按鈕 */}
      {safeFeatures.includes("share") && (
        <button
          className="flex items-center gap-1 cursor-pointer transition-colors hover:text-primary/70"
          onClick={handleShare}
          aria-label="分享"
          title="分享"
        >
          <Share2 className="h-3.5 w-3.5" />
        </button>
      )}
    </div>
  )
}

// 狀態標籤組件 (僅用於討論)
function StatusBadge({
  status,
  isHot,
  isNew,
  contentType,
}: { status?: string; isHot?: boolean; isNew?: boolean; contentType: ContentType }) {
  const { variant } = useContentCard()

  // 對於討論類型，不顯示 "published" 狀態
  const shouldShowStatus = status && !(contentType === "thread" && status === "published")

  if (!shouldShowStatus && !isHot && !isNew) return null

  return (
    <div className="flex gap-2">
      {shouldShowStatus && (
        <Badge
          variant={status === "draft" ? "outline" : status === "pending" ? "secondary" : "default"}
          className={cn(
            variant === "grid" ? "text-xs py-0" : "",
            status === "draft"
              ? "bg-blue-50 text-blue-600 dark:bg-blue-950 dark:text-blue-300"
              : status === "pending"
                ? "bg-amber-50 text-amber-600 dark:bg-amber-950 dark:text-amber-300"
                : "",
          )}
        >
          {status === "draft" ? "草稿" : status === "pending" ? "審核中" : "已發布"}
        </Badge>
      )}
      {isHot && (
        <Badge variant="destructive" className={variant === "grid" ? "text-xs py-0" : ""}>
          熱門
        </Badge>
      )}
      {isNew && (
        <Badge variant="default" className={variant === "grid" ? "text-xs py-0" : ""}>
          新帖
        </Badge>
      )}
    </div>
  )
}

// 語義類型標籤組件
function SemanticTypeBadge({ type }: { type?: SemanticType }) {
  const { variant } = useContentCard()

  if (!type) return null

  const typeConfig = semanticTypeConfig[type]

  if (!typeConfig) return null

  return (
    <Badge
      variant="outline"
      className={cn("flex items-center gap-1", variant === "grid" ? "text-xs py-0" : "", typeConfig.color)}
    >
      {typeConfig.icon}
      <span>{typeConfig.label}</span>
    </Badge>
  )
}

// 來源類型標籤組件 (僅用於觀點卡) - 更新為使用 contribution_type
function ContributionTypeBadge({ contributionType }: { contributionType?: ContributionType }) {
  if (!contributionType) return null

  const config = contributionTypeConfig[contributionType]

  if (!config || !config.show) return null

  return (
    <Badge variant="default" className={cn("flex items-center gap-1", config.color)}>
      <span>{config.badge}</span>
    </Badge>
  )
}

// 修改 CardVariantComponent 組件，使用 contribution_type
function CardVariantComponent(props: ContentCardProps) {
  const router = useRouter()
  const {
    contentType,
    id,
    title,
    content,
    author,
    timestamp,
    topics,
    tags,
    subtopics,
    semanticType,
    stats,
    className,
    onAction,
    forceBookmarked,
  } = props

  // 添加调试信息
  console.log("CardVariantComponent props:", {
    contentType,
    id,
    title,
    contentLength: content ? content.length : 0,
    contentSample: content ? content.substring(0, 50) : null,
    isNull: content === null,
    isUndefined: content === undefined,
    isEmptyString: content === "",
  })

  const path = contentType === "viewpoint" ? `/card/${id}` : `/thread/${id}`

  const handleCardClick = () => {
    router.push(path)
  }

  // 觀點卡特有屬性 - 更新為使用 contribution_type
  const card_type = contentType === "viewpoint" && "card_type" in props ? props.card_type : undefined
  const contribution_type =
    contentType === "viewpoint" && "contribution_type" in props ? props.contribution_type : undefined
  const originalSource = contentType === "viewpoint" && "originalSource" in props ? props.originalSource : undefined
  const originalAuthor = contentType === "viewpoint" && "originalAuthor" in props ? props.originalAuthor : undefined

  // 討論串特有屬性
  const status = contentType === "thread" && "status" in props ? props.status : undefined
  const isHot = contentType === "thread" && "isHot" in props ? props.isHot : undefined
  const isNew = contentType === "thread" && "isNew" in props ? props.isNew : undefined

  return (
    <Card
      className={cn(
        "overflow-hidden transition-all hover:shadow-md cursor-pointer min-h-[200px] max-h-fit flex flex-col",
        className,
      )}
      onClick={handleCardClick}
    >
      <CardHeader className="p-4 pb-3 space-y-3">
        <div className="flex items-start justify-between gap-2">
          <div className="flex items-center gap-2 flex-wrap">
            {/* 語義類型標籤 */}
            <SemanticTypeBadge type={semanticType} />

            {/* 觀點卡特有：貢獻類型 - 移到左側 */}
            {contentType === "viewpoint" && contribution_type && (
              <ContributionTypeBadge contributionType={contribution_type} />
            )}

            {/* 主題標籤 */}
            <TopicBadges topics={topics} />
          </div>

          <div className="flex items-center gap-2">
            <StatusBadge
              status={status}
              isHot={contentType === "thread" ? isHot : undefined}
              isNew={contentType === "thread" ? isNew : undefined}
              contentType={contentType}
            />
          </div>
        </div>

        {/* 標題 - 傳遞 originalAuthor */}
        <ContentTitle title={title} id={id} contentType={contentType} originalAuthor={originalAuthor} />
      </CardHeader>

      <CardContent className="px-4 pb-2 pt-1 flex-grow flex flex-col">
        {/* 內容 */}
        <div onClick={handleCardClick} className="block flex-grow">
          <ContentBody content={content} />
        </div>

        {/* 標籤 */}
        <ContentTags tags={tags} subtopics={subtopics} />

        {/* 原始來源 (觀點卡) */}
        {contentType === "viewpoint" && originalSource && (
          <div
            className="text-xs text-muted-foreground flex items-center mt-2"
            onClick={(e) => e.stopPropagation()} // Prevent card click when clicking on external link
          >
            <span>原始出處: </span>
            <a
              href={originalSource}
              target="_blank"
              rel="noopener noreferrer"
              className="ml-1 flex items-center hover:text-primary"
              onClick={(e) => e.stopPropagation()}
            >
              <span>外部連結</span>
              <ExternalLink className="h-3 w-3 ml-1" />
            </a>
          </div>
        )}
      </CardContent>

      <CardFooter
        className="flex items-center justify-between border-t border-border/40 px-4 py-2 text-sm"
        onClick={(e) => {
          e.stopPropagation()
          e.preventDefault()
        }}
      >
        {/* 作者和時間 */}
        <ContentMeta author={author} timestamp={timestamp} />

        {/* 統計信息 - 傳遞 id 以便互動 */}
        <ContentStats stats={stats} id={id} forceBookmarked={forceBookmarked} />
      </CardFooter>
    </Card>
  )
}

// 修改 GridCard 組件，正確處理 props
function GridCard(props: ContentCardProps) {
  const router = useRouter()
  const {
    contentType,
    id,
    title,
    content,
    author,
    timestamp,
    topics,
    tags,
    subtopics,
    semanticType,
    stats,
    className,
    onAction,
    forceBookmarked,
  } = props
  const { isAuthenticated } = useAuth()
  const { toast } = useToast()
  const [likeCount, setLikeCount] = useState(stats?.likes || 0)
  const [hasLiked, setHasLiked] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)

  const path = contentType === "viewpoint" ? `/card/${id}` : `/thread/${id}`
  const tagList = contentType === "viewpoint" ? subtopics || [] : tags || []
  const commentCount = contentType === "viewpoint" ? stats?.comments : stats?.replies

  // 獲取原作者信息
  const originalAuthor = contentType === "viewpoint" && "originalAuthor" in props ? props.originalAuthor : undefined

  // 處理顯示標題
  const displayTitle = contentType === "viewpoint" && originalAuthor ? `${originalAuthor}：${title}` : title

  const handleCardClick = () => {
    router.push(path)
  }



  // 處理點讚
  const handleLike = async (e: React.MouseEvent) => {
    e.stopPropagation()

    if (!isAuthenticated) {
      toast({
        title: "請先登入",
        description: "您需要登入才能進行此操作",
        action: (
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push("/auth/login")}
            className="border-primary text-primary hover:bg-primary/10"
          >
            登入
          </Button>
        ),
        variant: "default",
      })
      return
    }

    if (isProcessing) {
      return // 防止重複處理
    }

    setIsProcessing(true)

    // 確保 id 是字符串並檢查格式
    const itemId = typeof id === "number" ? id.toString() : id
    if (!isValidUUID(itemId)) {
      console.log("跳過反應操作：無效的 UUID 格式", itemId)
      setIsProcessing(false)
      return
    }

    // 樂觀更新 UI
    const wasLiked = hasLiked
    setHasLiked(!wasLiked)
    setLikeCount((prev) => (wasLiked ? Math.max(0, prev - 1) : prev + 1))

    try {
      const response = await fetch("/api/reactions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          itemType: contentType === "viewpoint" ? "card" : "thread",
          itemId: itemId,
          reactionType: "like",
        }),
      })

      const data = await response.json()

      if (!response.ok || !data.success) {
        // 如果請求失敗，恢復原來的狀態
        setHasLiked(wasLiked)
        setLikeCount((prev) => (wasLiked ? prev + 1 : Math.max(0, prev - 1)))

        toast({
          title: "操作失敗",
          description: data.error || "請稍後再試",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error toggling reaction:", error)

      // 如果請求失敗，恢復原來的狀態
      setHasLiked(wasLiked)
      setLikeCount((prev) => (wasLiked ? prev + 1 : Math.max(0, prev - 1)))

      toast({
        title: "操作失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      // 立即關閉處理中狀態
      setIsProcessing(false)
    }
  }

  // 獲取用戶反應狀態
  React.useEffect(() => {
    if (!isAuthenticated) return

    const fetchReactionStatus = async () => {
      try {
        // 確保 id 是字符串並檢查格式
        const itemId = typeof id === "number" ? id.toString() : id
        if (!isValidUUID(itemId)) {
          console.log("跳過反應狀態檢查：無效的 UUID 格式", itemId)
          return
        }

        // 檢查用戶是否已點讚
        const likeRes = await fetch(
          `/api/reactions/check?itemType=${contentType === "viewpoint" ? "card" : "thread"}&itemId=${itemId}&reactionType=like`,
        )

        // 檢查響應是否成功
        if (!likeRes.ok) {
          console.error("Error checking like status:", await likeRes.text())
          return
        }

        const likeData = await likeRes.json()
        if (likeData.success) {
          setHasLiked(!!likeData.data.hasReacted)
        }
      } catch (error) {
        console.error("Error checking reaction status:", error)
      }
    }

    fetchReactionStatus()
  }, [id, contentType, isAuthenticated])

  // 獲取最新的反應計數
  React.useEffect(() => {
    const fetchReactionCounts = async () => {
      try {
        // 確保 id 是字符串並檢查格式
        const itemId = typeof id === "number" ? id.toString() : id
        if (!isValidUUID(itemId)) {
          console.log("跳過反應計數獲取：無效的 UUID 格式", itemId)
          return
        }

        const countRes = await fetch(
          `/api/reactions/count?itemType=${contentType === "viewpoint" ? "card" : "thread"}&itemId=${itemId}`,
        )

        // 檢查響應是否成功
        if (!countRes.ok) {
          console.error("Error fetching reaction counts:", await countRes.text())
          return
        }

        const countData = await countRes.json()

        if (countData.success) {
          setLikeCount(countData.data.like || 0)
        }
      } catch (error) {
        console.error("Error fetching reaction counts:", error)
      }
    }

    fetchReactionCounts()
  }, [id, contentType])

  return (
    <Card
      className={cn(
        "overflow-hidden border-border/40 hover:border-border/80 transition-colors h-full flex flex-col cursor-pointer hover:shadow-md",
        className,
      )}
      onClick={handleCardClick}
    >
      <CardHeader className="p-1.5 pb-0 flex flex-row items-start justify-between space-y-0">
        <div className="flex flex-wrap items-center gap-1">
          <SemanticTypeBadge type={semanticType} />

          {/* 只顯示第一個主題標籤 */}
          {topics && Array.isArray(topics) && topics.length > 0 && (
            <Badge variant="secondary" className="flex items-center gap-1 text-xs py-0">
              <span>{topics[0]}</span>
            </Badge>
          )}

          {/* 觀點卡特有：貢獻類型 */}
          {contentType === "viewpoint" && "contribution_type" in props && (
            <ContributionTypeBadge contributionType={props.contribution_type} />
          )}
        </div>

        <div className="flex items-center gap-1">
          <StatusBadge
            status={contentType === "thread" && "status" in props ? props.status : undefined}
            isHot={contentType === "thread" && "isHot" in props ? props.isHot : undefined}
            isNew={contentType === "thread" && "isNew" in props ? props.isNew : undefined}
            contentType={contentType}
          />
        </div>
      </CardHeader>

      <CardContent className="p-1.5 flex-grow space-y-0.5">
        {/* 標題 - 使用處理後的顯示標題 */}
        <h3 className="text-sm font-bold text-foreground line-clamp-2 mb-0.5">{displayTitle}</h3>

        {/* 作者信息 - 簡化 */}
        <div className="mb-0.5 flex items-center gap-1">
          <span className="text-xs text-gray-500">{author.name}</span>
          {timestamp && (
            <>
              <span className="text-xs text-gray-500">•</span>
              <span className="text-xs text-gray-500">{timestamp}</span>
            </>
          )}
        </div>

        {/* 內容 - 限制為2行，使用 TipTap 渲染 */}
        <div className="min-h-[28px]">
          <div className="line-clamp-2 text-muted-foreground text-xs">
            <RichTextContent content={content} className="overflow-visible" />
          </div>
        </div>
      </CardContent>

      <CardFooter className="p-1.5 pt-0 flex flex-col space-y-1 mt-auto">
        {/* 標籤 - 移到頂部 */}
        {tagList && Array.isArray(tagList) && tagList.length > 0 && (
          <div className="flex flex-wrap gap-1 w-full">
            {tagList.slice(0, 2).map((tag) => (
              <div
                key={tag}
                onClick={(e) => {
                  e.stopPropagation() // Prevent card click
                }}
              >
                <Badge variant="outline" className="text-xs py-0 cursor-pointer hover:bg-secondary">
                  #{tag}
                </Badge>
              </div>
            ))}
            {tagList.length > 2 && (
              <Badge variant="outline" className="text-xs py-0">
                +{tagList.length - 2}
              </Badge>
            )}
          </div>
        )}

        {/* 分隔線 - 減少間距 */}
        {tagList.length > 0 && <div className="w-full border-t border-border/30 my-0.5"></div>}

        {/* 互動按鈕 - 移到底部 */}
        <div className="flex justify-between items-center w-full">
          <div className="flex items-center gap-0.5">
            <button
              className={cn(
                "flex items-center gap-1 cursor-pointer transition-colors h-5 px-1",
                hasLiked ? "text-primary" : "hover:text-primary/70",
              )}
              onClick={handleLike}
              disabled={isProcessing}
              aria-label={`${likeCount} 個讚`}
              title={hasLiked ? "取消讚" : "點讚"}
            >
              <ThumbsUp className={cn("h-3 w-3", hasLiked && "fill-current")} />
              {likeCount > 0 && <span className="text-xs">{likeCount}</span>}
            </button>

            <div onClick={(e) => e.stopPropagation()}>
              <BookmarkButton
                itemId={id.toString()}
                itemType={contentType === "viewpoint" ? "card" : "thread"}
                showCount={true}
                disabled={!isAuthenticated}
                className="h-5 px-1 text-xs text-muted-foreground hover:text-primary/70"
                variant="ghost"
                size="sm"
                forceBookmarked={forceBookmarked}
              />
            </div>
          </div>

          {/* 評論數 */}
          {commentCount !== undefined && commentCount > 0 && (
            <div className="flex items-center text-xs text-muted-foreground">
              <MessageSquare className="h-3 w-3 mr-0.5" />
              {commentCount}
            </div>
          )}
        </div>
      </CardFooter>
    </Card>
  )
}

// 修改 CompactCard 組件，使用 contribution_type
function CompactCard(props: ContentCardProps) {
  const router = useRouter()
  const { contentType, id, title, author, timestamp, stats, className, onAction, forceBookmarked } = props
  const path = contentType === "viewpoint" ? `/card/${id}` : `/thread/${id}`

  // 討論串特有屬性
  const status = contentType === "thread" && "status" in props ? props.status : undefined
  const isHot = contentType === "thread" && "isHot" in props ? props.isHot : undefined
  const isNew = contentType === "thread" && "isNew" in props ? props.isNew : undefined

  // 獲取原作者信息
  const originalAuthor = contentType === "viewpoint" && "originalAuthor" in props ? props.originalAuthor : undefined

  // 處理顯示標題
  const displayTitle = contentType === "viewpoint" && originalAuthor ? `${originalAuthor}：${title}` : title

  return (
    <div
      onClick={() => router.push(path)}
      className={`border rounded-md p-4 flex items-center justify-between ${className} hover:bg-muted/50 transition-colors cursor-pointer`}
    >
      <div className="flex items-center gap-4 flex-1">
        <div className="flex-1">
          <div className="flex items-center gap-2">
            <h3 className="font-medium text-base leading-snug line-clamp-2 hover:text-primary">{displayTitle}</h3>
            {contentType === "thread" && <StatusBadge status={status} isHot={isHot} isNew={isNew} contentType={contentType} />}
          </div>
          <div className="flex items-center gap-4 mt-2">
            <ContentMeta author={author} timestamp={timestamp} />
            <ContentStats stats={stats} id={id} forceBookmarked={forceBookmarked} />
          </div>
        </div>
      </div>
      {contentType === "thread" && status === "draft" && (
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              onAction?.("edit", id)
            }}
          >
            編輯
          </Button>
        </div>
      )}
    </div>
  )
}

// 修改 QuoteCard 组件，使用 contribution_type
function QuoteCard(props: ContentCardProps) {
  const { contentType, id, title, content, author, semanticType, className } = props
  const path = contentType === "viewpoint" ? `/card/${id}` : `/thread/${id}`

  // 获取原作者信息
  const originalAuthor = contentType === "viewpoint" && "originalAuthor" in props ? props.originalAuthor : undefined

  // 处理显示标题
  const displayTitle = contentType === "viewpoint" && originalAuthor ? `${originalAuthor}：${title}` : title

  // 获取标签
  const cardTags = props.contentType === "viewpoint" ? props.subtopics : props.tags

  // 获取是否为意见领袖
  const isLeader =
    contentType === "viewpoint" && "contribution_type" in props ? props.contribution_type === "top_author" : false

  // 获取类型配置
  const typeConfig = semanticTypeConfig[semanticType || "concept"] || semanticTypeConfig.concept
  const sourceConfig = contributionTypeConfig[isLeader ? "top_author" : "community"]

  // 截断内容
  const truncateText = (text: string, maxLength: number) => {
    if (!text || text.length <= maxLength) return text || ""
    return text.substring(0, maxLength) + "..."
  }

  return (
    <div className="my-4">
      <div className="border rounded-md overflow-hidden bg-card">
        <div className={`p-3 border-l-4 ${typeConfig.color.replace("bg-", "border-")}`}>
          <div className="flex flex-col gap-1">
            <div className="flex flex-wrap items-center gap-2">
              <Badge variant="outline" className={cn("flex items-center gap-1", typeConfig.color)}>
                {typeConfig.icon}
                <span>{typeConfig.label}</span>
              </Badge>

              {cardTags && cardTags.length > 0 && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <span>{cardTags[0]}</span>
                </Badge>
              )}

              {isLeader && sourceConfig.show && (
                <Badge variant="default" className={cn("flex items-center gap-1", sourceConfig.color)}>
                  <span>{sourceConfig.badge}</span>
                </Badge>
              )}
            </div>

            <div className="text-xs text-muted-foreground">作者：{author.name}</div>

            <h4 className="text-sm font-bold">
              <a href={path} className="text-primary hover:underline">
                {displayTitle}
              </a>
            </h4>

            <div className="text-xs text-muted-foreground line-clamp-2">{truncateText(content, 150)}</div>
          </div>
        </div>
      </div>
    </div>
  )
}

// 添加一个新的 TipTapQuoteCard 组件，用于 TipTap 编辑器集成
// 在 ContentCard 组件之前添加这个新组件
export function TipTapQuoteCard(props: CardQuoteProps) {
  // 从 props 中提取卡片数据，提供默认值
  const attrs = props.node?.attrs || {}
  const card = props.card || {}

  const cardId = attrs.cardId || (card as any).id || 0
  const cardTitle = attrs.cardTitle || (card as any).title || "未知標題"
  const cardContent = attrs.cardContent || (card as any).content || "未知內容"
  const cardAuthor = attrs.cardAuthor || (card as any).author || "未知作者"
  const cardType = attrs.cardType || (card as any).type || "concept"
  const cardTags = attrs.cardTags || (card as any).tags || []
  const isLeader = attrs.isLeader || (card as any).isLeader || false

  const typeConfig = semanticTypeConfig[cardType as SemanticType] || semanticTypeConfig.concept
  const sourceConfig = contributionTypeConfig[isLeader ? "top_author" : "community"]

  // 截断内容
  const truncateText = (text: string, maxLength: number) => {
    if (!text || text.length <= maxLength) return text || ""
    return text.substring(0, maxLength) + "..."
  }

  return (
    <NodeViewWrapper>
      <div className="my-4">
        <div className="border rounded-md overflow-hidden bg-card">
          <div className={`p-3 border-l-4 ${typeConfig.color.replace("bg-", "border-")}`}>
            <div className="flex flex-col gap-1">
              <div className="flex flex-wrap items-center gap-2">
                <Badge variant="outline" className={cn("flex items-center gap-1", typeConfig.color)}>
                  {typeConfig.icon}
                  <span>{typeConfig.label}</span>
                </Badge>

                {cardTags && cardTags.length > 0 && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <span>{cardTags[0]}</span>
                  </Badge>
                )}

                {isLeader && sourceConfig.show && (
                  <Badge variant="default" className={cn("flex items-center gap-1", sourceConfig.color)}>
                    <span>{sourceConfig.badge}</span>
                  </Badge>
                )}
              </div>

              <div className="text-xs text-muted-foreground">作者：{cardAuthor}</div>

              <h4 className="text-sm font-bold">
                <a href={`/card/${cardId}`} className="text-primary hover:underline">
                  {cardTitle}
                </a>
              </h4>

              <div className="text-xs text-muted-foreground line-clamp-2">{truncateText(cardContent, 150)}</div>
            </div>
          </div>
        </div>
      </div>
    </NodeViewWrapper>
  )
}

// 主組件
export function ContentCard({
  variant = "card",
  isInteractive = true,
  features = ["like", "dislike", "reply", "bookmark", "share"] as Feature[],
  ...props
}: ContentCardProps) {
  // 確保 features 是一個數組
  const safeFeatures = Array.isArray(features) ? features : (["like", "dislike", "reply", "bookmark", "share"] as Feature[])

  // 根據變體選擇適當的渲染組件
  const Component =
    {
      card: CardVariantComponent,
      compact: CompactCard,
      quote: QuoteCard,
      grid: GridCard,
    }[variant] || CardVariantComponent

  return (
    <ContentCardContext.Provider
      value={{
        contentType: props.contentType,
        variant,
        isInteractive,
        features: safeFeatures,
        semanticType: props.semanticType,
        status: props.contentType === "thread" && "status" in props ? props.status : undefined,
      }}
    >
      <Component {...props} />
    </ContentCardContext.Provider>
  )
}
